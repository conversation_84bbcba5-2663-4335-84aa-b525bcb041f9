from rdkit.Chem import Descriptors
from rdkit import Chem
from rdkit.Contrib.SA_Score import sascorer


def compute_selected_descriptors(smiles, selected_descriptors=['MolWt', 'HeavyAtomCount', 'RingCount', 'MolLogP', 'qed']):
    mol = Chem.MolFromSmiles(smiles)
    if mol:
        descriptors = {desc_name: Descriptors.__dict__.get(
            desc_name)(mol) for desc_name in selected_descriptors}
        return descriptors
    else:
        return {desc_name: None for desc_name in selected_descriptors}


def calculate_sascore(smiles):
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is not None:
            return sascorer.calculateScore(mol)
        else:
            return None
    except Exception as e:
        print(f"Error processing SMILES {smiles}: {str(e)}")
        return None
